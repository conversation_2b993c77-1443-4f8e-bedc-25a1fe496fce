geojson-3.2.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
geojson-3.2.0.dist-info/LICENSE.rst,sha256=-p3Lb58twTMe5PXVUqfYsuEd-0ExhDJFLRfyluPvwKc,1479
geojson-3.2.0.dist-info/METADATA,sha256=KrFXcWl9LM7vbXKO_SDMoYma59apz37qn2wQF_1fMlc,16647
geojson-3.2.0.dist-info/RECORD,,
geojson-3.2.0.dist-info/WHEEL,sha256=PZUExdf71Ui_so67QXpySuHtCi3-J3wvF4ORK6k_S8U,91
geojson-3.2.0.dist-info/top_level.txt,sha256=ZLdQwa2vrsL58DRBjE4uJf7lGdxtzlpSFmDKYMrUFA4,8
geojson/__init__.py,sha256=xvT-cZKScFt4PUTofTDz6C8h5xugYVVHbgqR39dYjjE,767
geojson/__pycache__/__init__.cpython-312.pyc,,
geojson/__pycache__/_version.cpython-312.pyc,,
geojson/__pycache__/base.cpython-312.pyc,,
geojson/__pycache__/codec.cpython-312.pyc,,
geojson/__pycache__/examples.cpython-312.pyc,,
geojson/__pycache__/factory.cpython-312.pyc,,
geojson/__pycache__/feature.cpython-312.pyc,,
geojson/__pycache__/geometry.cpython-312.pyc,,
geojson/__pycache__/mapping.cpython-312.pyc,,
geojson/__pycache__/utils.cpython-312.pyc,,
geojson/_version.py,sha256=hEmRM63TtdcAaHhhaMINtTBhf2n9roL9PSiogfoTC4s,81
geojson/base.py,sha256=Is_tnyNQJJFaZvaBjnIQTJujQIPmwFvC_OrxOXLyBn8,4486
geojson/codec.py,sha256=eqCRuaHeM5FpH4ofp-1DuoFRWVjMUOY-JWRMxokWbjc,1657
geojson/examples.py,sha256=Tloh60B6WG7vQEsVXX_HSN05HUPC0w2VuxFlOVVr6uI,2059
geojson/factory.py,sha256=mRU7nGv_19uY5fIVYz9qLBK3OqT_xU_z5rQpmZw7z3U,461
geojson/feature.py,sha256=xjlWIOqdmm4KwRul4vPU1barTMZGNxmQhQ84MBxg1jU,1749
geojson/geometry.py,sha256=enHxy1FfUkXOon-5VUPkWfbAnrjsG3h591fOxPJqDRg,4355
geojson/mapping.py,sha256=R8i7n_pRiLBb4SbIaW8NCFqjZoqwXm4TYWon25pXIWs,740
geojson/utils.py,sha256=uOaPFeiNnezmq_JAK4cVm6q1ForqpMlQS4qyJchg0So,7311

Metadata-Version: 2.4
Name: pyowm
Version: 3.5.0
Summary: A Python wrapper around OpenWeatherMap web APIs
Home-page: https://github.com/csparpa/pyowm
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Keywords: openweathermap web api client weather forecast uv alerting owm pollution meteostation agro agriculture
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Topic :: Software Development :: Libraries
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: requests<3,>=2.20.0
Requires-Dist: geojson<4,>=2.3.0
Requires-Dist: PySocks<2,>=1.7.1
Requires-Dist: requests[socks]
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

[![logo](https://raw.githubusercontent.com/csparpa/pyowm/master/logos/180x180.png)](https://github.com/csparpa)

#  PyOWM  
**A Python wrapper around OpenWeatherMap web APIs**

[![PyPI version](https://badge.fury.io/py/pyowm.svg)](https://badge.fury.io/py/pyowm)
[![PyPI - Downloads](https://img.shields.io/pypi/dm/pyowm.svg)](https://img.shields.io/pypi/dm/pyowm.svg)
<br>
[![PyPI - Python Version](https://img.shields.io/pypi/pyversions/pyowm.svg)](https://img.shields.io/pypi/pyversions/pyowm.svg)
<br>
[![Latest Release Documentation](https://readthedocs.org/projects/pyowm/badge/?version=latest)](https://pyowm.readthedocs.io/en/latest/)
[![Build Status](https://travis-ci.org/csparpa/pyowm.png?branch=develop)](https://travis-ci.org/csparpa/pyowm)
[![Coverage Status](https://coveralls.io/repos/github/csparpa/pyowm/badge.svg?branch=develop)](https://coveralls.io/github/csparpa/pyowm?branch=master)
<br>
<a href="https://www.buymeacoffee.com/LmAl1n9" target="_blank"><img src="https://www.buymeacoffee.com/assets/img/custom_images/black_img.png" alt="Buy Me A Coffee" style="height: auto !important;width: auto !important;" ></a>
[![Say Thanks!](https://img.shields.io/badge/Say%20Thanks-!-1EAEDB.svg)](https://saythanks.io/to/csparpa%40gmail.com)

##  What is it?
PyOWM is a client Python wrapper library for OpenWeatherMap (OWM) web APIs. It allows quick and easy consumption of OWM data from Python applications via a simple object model and in a human-friendly fashion.

PyOWM runs on Python 3.9+

**Former Dark Sky API users**: you can can use PyOWM to get [OpenWeatherMap's OneCall API](https://openweathermap.org/api/one-call-api) data as an easy replacement to Dark Sky

### What kind of data can I get with PyOWM ?
With PyOWM you can integrate into your code any of the following OpenWeatherMap web APIs:

 - **Weather API v3.0** + **OneCall API**, providing current weather data, weather forecasts, weather history
 - **Agro API v1.0**, providing soil data and satellite imagery search and download
 - **Air Pollution API v3.0**, providing data about CO, O3, NO2 and SO2
 - **UV Index API v3.0**, providing data about Ultraviolet exposition
 - **Stations API v3.0**, allowing to create and manage meteostation and publish local weather measurements
 - **Weather Alerts API v3.0**, allowing to set triggers on weather conditions and areas and poll for spawned alerts
 - **Image tiles** for several map layers provided by OWM
 - **Geocoding API v1.0** allowing to perform direct/reverse geocoding 


## In case of trouble...
Please **read the [FAQ](https://pyowm.readthedocs.io/en/latest/v3/faq.html)** before filing a new issue on GitHub! There are many common issues, therefore a fix for your issue might come easier than you think

 ##  Get started

### API key

As OpenWeatherMap APIs need a valid API key to allow responses, *PyOWM won't work if you don't provide one*. This stands for both free and paid (pro) subscription plans.
You can signup for a free API key [on the OWM website](https://home.openweathermap.org/users/sign_up)
Please notice that the free API subscription plan is subject to requests throttling.

### Example

With a free OWM API Key:

```python
from pyowm import OWM
from pyowm.utils import config
from pyowm.utils import timestamps

# ---------- FREE API KEY examples ---------------------

owm = OWM('your free OWM API key')
mgr = owm.weather_manager()


# Search for current weather in London (Great Britain) and get details
observation = mgr.weather_at_place('London,GB')
w = observation.weather

w.detailed_status         # 'clouds'
w.wind()                  # {'speed': 4.6, 'deg': 330}
w.humidity                # 87
w.temperature('celsius')  # {'temp_max': 10.5, 'temp': 9.7, 'temp_min': 9.0}
w.rain                    # {}
w.heat_index              # None
w.clouds                  # 75

# Will it be clear tomorrow at this time in Milan (Italy) ?
forecast = mgr.forecast_at_place('Milan,IT', 'daily')
answer = forecast.will_be_clear_at(timestamps.tomorrow())

# ---------- PAID API KEY example ---------------------

config_dict = config.get_default_config_for_subscription_type('professional')
owm = OWM('your paid OWM API key', config_dict)

# What's the current humidity in Berlin (Germany) ?
one_call_object = mgr.one_call(lat=52.5244, lon=13.4105)
one_call_object.current.humidity
```


##  Installation
Install with `pip` for your ease:

```shell
$ pip install pyowm
```

There are alternatives: _setuptools_, _Windows installers_ and common Linux package managers such as _Yaourt (Arch Linux)_
_YaST/Zypper (OpenSuse)_ (please refer to the documentation for more detail)

Eager to fetch the very latest updates to PyOWM? Install the development trunk (which might be unstable). Eg on Linux:

```shell
$ git clone https://github.com/csparpa/pyowm.git
$ cd pyowm && git checkout develop
$ pip install -r requirements.txt && python setup.py install
```

## Documentation
The library software API documentation is available on [Read the Docs](https://pyowm.readthedocs.io/en/latest/).

The [Code recipes](https://pyowm.readthedocs.io/en/latest/v3/code-recipes.html) section comes in handy!


## Community & Contributing

Here are [some cool projects](https://github.com/csparpa/pyowm/wiki/Community-Projects-using-PyOWM) that use PyOWM

_Contributors (coding, testing, packaging, reporting issues) are welcome!_ See the [the official documentation website](https://pyowm.readthedocs.io/) for details.


## License
[MIT license](https://github.com/csparpa/pyowm/blob/master/LICENSE)

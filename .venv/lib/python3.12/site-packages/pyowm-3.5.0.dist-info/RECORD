pyowm-3.5.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyowm-3.5.0.dist-info/METADATA,sha256=SfZ_UtK8tCeLTeL_HHDQ9DraAVXTV9zHkAzpV61TIrg,6937
pyowm-3.5.0.dist-info/RECORD,,
pyowm-3.5.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyowm-3.5.0.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
pyowm-3.5.0.dist-info/licenses/LICENSE,sha256=iC1fQONwjRYnfxaMPWyF8mOG76zbwQdm5v7uEtnS8Yk,1097
pyowm-3.5.0.dist-info/top_level.txt,sha256=vqOG1acMWNhQ33H5_rDuaOnXZpCPdu47vYOnpMz0lsk,6
pyowm/__init__.py,sha256=mGHoBLlDP6Mw5B7aBTt5y6KgUWy4wabdVkboq6glFqo,73
pyowm/__pycache__/__init__.cpython-312.pyc,,
pyowm/__pycache__/config.cpython-312.pyc,,
pyowm/__pycache__/constants.cpython-312.pyc,,
pyowm/__pycache__/owm.cpython-312.pyc,,
pyowm/agroapi10/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyowm/agroapi10/__pycache__/__init__.cpython-312.pyc,,
pyowm/agroapi10/__pycache__/agro_manager.cpython-312.pyc,,
pyowm/agroapi10/__pycache__/enums.cpython-312.pyc,,
pyowm/agroapi10/__pycache__/imagery.cpython-312.pyc,,
pyowm/agroapi10/__pycache__/polygon.cpython-312.pyc,,
pyowm/agroapi10/__pycache__/search.cpython-312.pyc,,
pyowm/agroapi10/__pycache__/soil.cpython-312.pyc,,
pyowm/agroapi10/__pycache__/uris.cpython-312.pyc,,
pyowm/agroapi10/agro_manager.py,sha256=2b9OKrQn0TgP_EBN-RCQndW8HhQodAc0OSOnbylrjKc,15342
pyowm/agroapi10/enums.py,sha256=aBC5dPVPxVJmRahC7RPaDeTdCJN-m_7U30gDd_9PQGM,1701
pyowm/agroapi10/imagery.py,sha256=Aq_L_96ODexsCAB3rRF0Pr6wC6Gc9m3Pm0U4IEFwlyc,6708
pyowm/agroapi10/polygon.py,sha256=_mQ5sYFJzU_fXU-7wXVln-CKQXjdWj6tAL0WsbA8AG0,2665
pyowm/agroapi10/search.py,sha256=aDjgyXwwWea_nM2_EWaQeQt-FiPbzyk800Zv8_iytu0,10413
pyowm/agroapi10/soil.py,sha256=8V_umdEvJrGOhl4oTP5qArORObtrHZWFXT3NGLKSOi0,4632
pyowm/agroapi10/uris.py,sha256=usLlXv8x9_ET2NeLvMsZIqm_TY2ZCbOe_eOjhhPwTNI,411
pyowm/airpollutionapi30/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyowm/airpollutionapi30/__pycache__/__init__.cpython-312.pyc,,
pyowm/airpollutionapi30/__pycache__/airpollution_client.cpython-312.pyc,,
pyowm/airpollutionapi30/__pycache__/airpollution_manager.cpython-312.pyc,,
pyowm/airpollutionapi30/__pycache__/airstatus.cpython-312.pyc,,
pyowm/airpollutionapi30/__pycache__/coindex.cpython-312.pyc,,
pyowm/airpollutionapi30/__pycache__/no2index.cpython-312.pyc,,
pyowm/airpollutionapi30/__pycache__/ozone.cpython-312.pyc,,
pyowm/airpollutionapi30/__pycache__/so2index.cpython-312.pyc,,
pyowm/airpollutionapi30/__pycache__/uris.cpython-312.pyc,,
pyowm/airpollutionapi30/airpollution_client.py,sha256=MOLW64nwg5dFb1DzEhOCA-ykXx5e-rjih54ChB5I8pI,6316
pyowm/airpollutionapi30/airpollution_manager.py,sha256=8lWX_J9oP6ZyGF-CKfLX3kX3foVqL221wCWjt-W-erE,13561
pyowm/airpollutionapi30/airstatus.py,sha256=g6tQjd1Tp7uCxI_Y-rhsiqRoANElDV_Ior6d70eP1Dg,5170
pyowm/airpollutionapi30/coindex.py,sha256=eTw8SbMjbMCECbJl5kQ1_xBYAgJdAkqvmtXeD8BaMvM,5983
pyowm/airpollutionapi30/no2index.py,sha256=Lchr52vUBuwbf-WlZQ-dKeCTkI3g21oL_fcg9_7nGVQ,6084
pyowm/airpollutionapi30/ozone.py,sha256=koiIVQ9NS9wY4SfRFQJJboZnQr9Obd4lpdUfxL9cBVc,5574
pyowm/airpollutionapi30/so2index.py,sha256=vfG6FD6HNqU_4WfClOI1akcsoV4edxbGw71DjfBMilg,5503
pyowm/airpollutionapi30/uris.py,sha256=kZDX4Z_BW7JVsFSLreh6LncM4Fm38bP1Jk_xikTf02E,440
pyowm/alertapi30/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyowm/alertapi30/__pycache__/__init__.cpython-312.pyc,,
pyowm/alertapi30/__pycache__/alert.cpython-312.pyc,,
pyowm/alertapi30/__pycache__/alert_manager.cpython-312.pyc,,
pyowm/alertapi30/__pycache__/condition.cpython-312.pyc,,
pyowm/alertapi30/__pycache__/enums.cpython-312.pyc,,
pyowm/alertapi30/__pycache__/trigger.cpython-312.pyc,,
pyowm/alertapi30/__pycache__/uris.cpython-312.pyc,,
pyowm/alertapi30/alert.py,sha256=cey_tJCFadIJpOj4krFn0v-81rfWmew-SuM4ji1T92Y,4519
pyowm/alertapi30/alert_manager.py,sha256=cfdmDhI7ai9Q270rGY3bd7vHRTeOoayZypxClf_B07g,10232
pyowm/alertapi30/condition.py,sha256=B_1U7OfFboZvED-3NFyqINjoBGx8C1ZYmWnBcvy6sls,2493
pyowm/alertapi30/enums.py,sha256=W3Wp80dd3eDBB4AK5z32j1QNB-8IMgvmuEAMhjZ9DV4,1608
pyowm/alertapi30/trigger.py,sha256=a3if0kEkW351zzH25z-su7FV_a6cCsI3_4XoNNBZQRI,8730
pyowm/alertapi30/uris.py,sha256=uGVOhgj7jZ0ai1anabHdHBCeiGvt5UaQSIyNjLV_Y1Y,258
pyowm/commons/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyowm/commons/__pycache__/__init__.cpython-312.pyc,,
pyowm/commons/__pycache__/cityidregistry.cpython-312.pyc,,
pyowm/commons/__pycache__/databoxes.cpython-312.pyc,,
pyowm/commons/__pycache__/enums.cpython-312.pyc,,
pyowm/commons/__pycache__/exceptions.cpython-312.pyc,,
pyowm/commons/__pycache__/http_client.cpython-312.pyc,,
pyowm/commons/__pycache__/image.cpython-312.pyc,,
pyowm/commons/__pycache__/tile.cpython-312.pyc,,
pyowm/commons/__pycache__/uris.cpython-312.pyc,,
pyowm/commons/cityidregistry.py,sha256=UhNywOQ8k6v4PHQKW92zHg5cJMMIB4yJuAvXBecjCyE,7291
pyowm/commons/cityids/__init__.py,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
pyowm/commons/cityids/__pycache__/__init__.cpython-312.pyc,,
pyowm/commons/cityids/cities.db.bz2,sha256=nmlE9OkwcdGsXGe3nEPdOP14v8vbHB0QFW7MsfN_t9Y,4440783
pyowm/commons/databoxes.py,sha256=cdNxr9q3RTNt5M_ShUU-YyMnvBi8FhtqAN7DdqK-U4s,1635
pyowm/commons/enums.py,sha256=Dmapo2XlCXUySCc3RNIYZMeI6eHEZmwq3FaWTMNY3DI,1950
pyowm/commons/exceptions.py,sha256=HIxFNqED7tPsbv4VC-mr9_m0_dZkU_2AeRu-GTv6jlw,1882
pyowm/commons/http_client.py,sha256=UH5-aGzO7LsbkyM-vI_VEpuHNfFjpGZdwtStF56ViQI,14187
pyowm/commons/image.py,sha256=q3Arn8zzy88Rba6gcNH5zVHLVw8omvuB55HRXsjHzPo,1587
pyowm/commons/tile.py,sha256=XaO623oPSPkJsc9qQTCRQpSaMYGWSL2NQNhAbdtR6D8,4232
pyowm/commons/uris.py,sha256=O22dMTKN4z9_qMNZUrGis6qVl4xu6_Ji8Vd_H4liQJ4,168
pyowm/config.py,sha256=LwKfAIhiJ7VZ2o36LzWj9BwsLJJ_vr1TtHLh-jI2rzs,482
pyowm/constants.py,sha256=wCwZ0wuBqWSGb06XT5AQ7Kv3GVKr9qkwAuxfi4NHVhQ,788
pyowm/geocodingapi10/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyowm/geocodingapi10/__pycache__/__init__.cpython-312.pyc,,
pyowm/geocodingapi10/__pycache__/geocoding_manager.cpython-312.pyc,,
pyowm/geocodingapi10/geocoding_manager.py,sha256=Eh8PYUXZq5jxlxiq3KQcCaFPXC1qIL0yKjyE9tMuy9o,3084
pyowm/owm.py,sha256=VtD2E8UdLuYZkZGvL_xnwssZ47ScUbHbTIfXXfCH4lQ,5092
pyowm/stationsapi30/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyowm/stationsapi30/__pycache__/__init__.cpython-312.pyc,,
pyowm/stationsapi30/__pycache__/buffer.cpython-312.pyc,,
pyowm/stationsapi30/__pycache__/measurement.cpython-312.pyc,,
pyowm/stationsapi30/__pycache__/persistence_backend.cpython-312.pyc,,
pyowm/stationsapi30/__pycache__/station.cpython-312.pyc,,
pyowm/stationsapi30/__pycache__/stations_manager.cpython-312.pyc,,
pyowm/stationsapi30/__pycache__/uris.cpython-312.pyc,,
pyowm/stationsapi30/buffer.py,sha256=439F_eVQXpvx4E7HNzVjRNxiO1IS9TQQN4bZLum6Tok,3242
pyowm/stationsapi30/measurement.py,sha256=aSxoJ2IiEaBsiPFleza9ZRC17dSTp126zSagPfmASG8,11796
pyowm/stationsapi30/persistence_backend.py,sha256=OI6A22ifV9MyZ6tXPWS3TBPe-qsNXJrLqzlv8pj2MKU,2313
pyowm/stationsapi30/station.py,sha256=-fz7iLlajydMzRDFD381PjTfp1gdFQNM1YU_MA_jb0s,6069
pyowm/stationsapi30/stations_manager.py,sha256=aeQtmtuX0bWua0AhoO9TsQBlg0g1GFZoOUcFpFPKTGw,10955
pyowm/stationsapi30/uris.py,sha256=zaoCEkfvhD3tLpBVDuf5Gp6ALPLtUEa3tB-8KpvcNTg,223
pyowm/tiles/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyowm/tiles/__pycache__/__init__.cpython-312.pyc,,
pyowm/tiles/__pycache__/enums.cpython-312.pyc,,
pyowm/tiles/__pycache__/tile_manager.cpython-312.pyc,,
pyowm/tiles/__pycache__/uris.cpython-312.pyc,,
pyowm/tiles/enums.py,sha256=4thYXegvxPHJtRyz_inJud4b9ZnhFQpwvkOxt85lVkw,255
pyowm/tiles/tile_manager.py,sha256=_A-zufsDWrujiRgQZmJXqoBaxhYzu_q1Pi27gK_0H4o,2168
pyowm/tiles/uris.py,sha256=KIOw-OD7Z8ZExpg5WjWKnVpnNz9ScC476sK7oBLsGmk,121
pyowm/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyowm/utils/__pycache__/__init__.cpython-312.pyc,,
pyowm/utils/__pycache__/config.cpython-312.pyc,,
pyowm/utils/__pycache__/decorators.cpython-312.pyc,,
pyowm/utils/__pycache__/formatting.cpython-312.pyc,,
pyowm/utils/__pycache__/geo.cpython-312.pyc,,
pyowm/utils/__pycache__/measurables.cpython-312.pyc,,
pyowm/utils/__pycache__/strings.cpython-312.pyc,,
pyowm/utils/__pycache__/timestamps.cpython-312.pyc,,
pyowm/utils/__pycache__/weather.cpython-312.pyc,,
pyowm/utils/config.py,sha256=OEgg6HKX00v8dnp61XlcLQN5PeHq65E4sN3B-Oqg_vo,2318
pyowm/utils/decorators.py,sha256=mZktzDUnZIMfuVSPQ__ofA7I68UJv7HCfiZ-mC3zg_I,1239
pyowm/utils/formatting.py,sha256=IlU9QaWeeOy9dfSUSpHPnkzENBWaw57nVTaNhHdRUsY,5960
pyowm/utils/geo.py,sha256=DJRu_fBzuYmMp023eruBoveqG5pcocP1n0UhvC4S7Go,13168
pyowm/utils/measurables.py,sha256=fwaZgiQlxoiutXqX9KreqvSd1JSmlEJZanZ7FSCyZ_s,7259
pyowm/utils/strings.py,sha256=vxYSYACGtmCL_-a1F-knivMCnoffwIAXWCnurpPPEdc,911
pyowm/utils/timestamps.py,sha256=IChD9CpBxGDoSoAKjgBnaNc2WSeNZTduV7ToK7AF-w0,9450
pyowm/utils/weather.py,sha256=0cYLDWdQ1jF_SZWo_5isbujX0hcMWJqdJTXOGqaT4pk,4322
pyowm/uvindexapi30/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyowm/uvindexapi30/__pycache__/__init__.cpython-312.pyc,,
pyowm/uvindexapi30/__pycache__/uris.cpython-312.pyc,,
pyowm/uvindexapi30/__pycache__/uv_client.cpython-312.pyc,,
pyowm/uvindexapi30/__pycache__/uvindex.cpython-312.pyc,,
pyowm/uvindexapi30/__pycache__/uvindex_manager.cpython-312.pyc,,
pyowm/uvindexapi30/uris.py,sha256=YApp5Grbs8Z6SuSoEF4iY5pykDOGRRhZniKdSIfqXac,192
pyowm/uvindexapi30/uv_client.py,sha256=rlEj2cgQwxKONhS74W7_8qQZQP19619xRKZhE7kBwHM,3087
pyowm/uvindexapi30/uvindex.py,sha256=DV2Rm_oh7QuI3PtPL9A8TPktu053oZCo2jRqcCqZOwg,5388
pyowm/uvindexapi30/uvindex_manager.py,sha256=AYktI_tUeBUkIQZVSsLGpSpI6JGOnDdp4uVMdPwBhks,4901
pyowm/weatherapi30/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyowm/weatherapi30/__pycache__/__init__.cpython-312.pyc,,
pyowm/weatherapi30/__pycache__/forecast.cpython-312.pyc,,
pyowm/weatherapi30/__pycache__/forecaster.cpython-312.pyc,,
pyowm/weatherapi30/__pycache__/historian.cpython-312.pyc,,
pyowm/weatherapi30/__pycache__/location.cpython-312.pyc,,
pyowm/weatherapi30/__pycache__/national_weather_alert.cpython-312.pyc,,
pyowm/weatherapi30/__pycache__/observation.cpython-312.pyc,,
pyowm/weatherapi30/__pycache__/one_call.cpython-312.pyc,,
pyowm/weatherapi30/__pycache__/stationhistory.cpython-312.pyc,,
pyowm/weatherapi30/__pycache__/uris.cpython-312.pyc,,
pyowm/weatherapi30/__pycache__/weather.cpython-312.pyc,,
pyowm/weatherapi30/__pycache__/weather_manager.cpython-312.pyc,,
pyowm/weatherapi30/__pycache__/weathercoderegistry.cpython-312.pyc,,
pyowm/weatherapi30/forecast.py,sha256=v9FQxL3SabUHtt2i1CHMz6v1Ap8-LQO2hz95MumBINw,5845
pyowm/weatherapi30/forecaster.py,sha256=Aj-eD7bj7WekNctB-HDqTkMB_caqbnTVV1Zk0BnyzMs,17285
pyowm/weatherapi30/historian.py,sha256=02JhW6sIwO6ibp4jZBd5qjld-u5JZz5qjIJzQo-kzoI,9995
pyowm/weatherapi30/location.py,sha256=TNAon9ymUxXYoUE8_d1LOzQGRQFtbRJrefQT4Vn54BU,4208
pyowm/weatherapi30/national_weather_alert.py,sha256=RbgsvAj7E5UGpWnqBdTQkS4sGZW2GMy7qYZqe32pl-E,4486
pyowm/weatherapi30/observation.py,sha256=k80r4wBAARZSXIfbBxxnCyahG-gLJ_r-IMAkpfYKLl4,6191
pyowm/weatherapi30/one_call.py,sha256=42n6-RZ2jckyMUNr_12uLNE4AnPx1RiTZ4QzDRh-l-Y,4272
pyowm/weatherapi30/stationhistory.py,sha256=AD26Am_dyL0GfV2RhlJAUXMWDYsqmtn0-Euqw8moNrc,5742
pyowm/weatherapi30/uris.py,sha256=b9NvZSOuKXchMMvNUSa_d1rLJ1DoKxPzSNagiBqQU5o,623
pyowm/weatherapi30/weather.py,sha256=Znq7GsEko-AcvUmsFJzI3Lr7KVUW2R6FjAbIcn8G5kU,22704
pyowm/weatherapi30/weather_manager.py,sha256=5lm_jsVXD5WiTDTTI2x5v_LRR1-Q7gQ5R9zAwJUz5T4,26752
pyowm/weatherapi30/weathercoderegistry.py,sha256=22bRr2ahGr8y0WDt1Os_tU56mJZqAJcj3_K4xf7vrZY,2401

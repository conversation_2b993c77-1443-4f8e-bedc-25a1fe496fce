import socket
import asyncio
import json
from datetime import datetime
from typing import List, Dict, Any


# https://github.com/modelcontextprotocol/python-sdk
from mcp.server.fastmcp import FastMCP
from mcp.server.fastmcp.utilities.logging import get_logger

# https://github.com/lmstudio-ai/lmstudio-python
import lmstudio as lms

# https://github.com/deedy5/duckduckgo_search
from ddgs import DDGS
from ddgs.exceptions import (
    DDGSException,
    RatelimitException,
    TimeoutException,
)

# https://github.com/csparpa/pyowm
from pyowm import OWM

logger = get_logger(__name__)


def get_lan_ip() -> str:
    """Get the LAN IP of the machine (e.g., 192.168.x.x)."""
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        # Doesn’t need to be reachable, just used to get local IP
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
    except OSError:
        ip = "127.0.0.1"
    finally:
        s.close()
    return ip


mcp = FastMCP(
    name="TestSearch",
    instructions="Effectue des recherches sur DuckDuckGo",
)


@mcp.resource("resource://sys/info")
def sys_info() -> str:
    """Informations du serveur MCP"""
    data = {
        "lan_ip": get_lan_ip(),
        "hostname": socket.gethostname(),
        "timestamp": datetime.now().isoformat(),
        "status": "active",
    }
    return json.dumps(data, indent=2)


@mcp.tool()
async def get_current_model() -> str:
    """Get the currently loaded model in LM Studio.

    Returns:
        The name of the currently loaded model.
    """
    try:
        model = lms.llm()
        return json.dumps(model.get_info().to_dict(), indent=2)
    except AttributeError as e:
        return json.dumps({"error": f"AttributeError: {str(e)}"})
    except lms.LMStudioError as e:
        return json.dumps({"error": f"LMStudioError: {str(e)}"})


@mcp.tool()
def current_weather(city_name: str) -> str:
    """Get current weather information for a French city.

    Args:
        city_name: Name of the city in France

    Returns:
        Detailed weather information including temperature, humidity, wind, and conditions
    """
    if city_name is None or city_name == "":
        return "Aucune ville spécifiée."

    # OpenWeatherMap API Key:
    #   Make an account here : https://home.openweathermap.org/
    #   Check your API key here : https://home.openweathermap.org/api_keys
    owm = OWM("********************************")
    mgr = owm.weather_manager()

    # https://api.openweathermap.org/data/2.5/weather?q=" + city + "," + country + "&appid=" + api_key + "&units=" + id(weather_units) + "&lang=" + id(weather_language));
    # https://api.openweathermap.org/data/2.5/weather?q=evreux,france&appid=********************************&units=metric&lang=fr
    # https://openweathermap.org/current#fields_json
    weather_observation = mgr.weather_at_place(f"{city_name},fr")
    if weather_observation is None or weather_observation.weather is None:
        return f"Aucune donnée météo disponible pour {city_name} (FR)."
    wind = weather_observation.weather.wind()
    temperature = weather_observation.weather.temperature("celsius")
    return (
        f"À {weather_observation.location.name} (France, latitude : {weather_observation.location.lat}, longitude : {weather_observation.location.lon}), la météo actuelle est la suivante :\n"
        f"Statut détaillé : {weather_observation.weather.detailed_status}\n"
        f"Vent : {wind['speed'] * 3.6:.2f} km/h, direction: {wind['deg']} deg.\n"
        f"Température : {temperature['temp']} °C\n"
        f"Température ressentie : {temperature['feels_like']} °C\n"
        f"Pression atmosphérique : {weather_observation.weather.pressure['press']} mbar\n"
        f"Humidity : {weather_observation.weather.humidity} %\n"
        f"Précipitations : {0 if len(weather_observation.weather.rain) == 0 else weather_observation.weather.rain}\n"
        f"Couverture nuageuse : {weather_observation.weather.clouds} %\n"
    )


@mcp.tool()
def search_web(
    query: str,
    max_results: int = 10,
) -> List[Dict[str, Any]]:
    """
    Effectue une recherche web avec DuckDuckGo

    Args:
        query: Terme de recherche
        max_results: Nombre maximum de résultats (1-20, défaut: 10)

    Returns:
        Liste des résultats de recherche avec titre, URL, snippet et date
    """
    try:
        max_results = max(1, min(max_results, 20))

        logger.info("Recherche web - query: '%s' (max_results=%d)", query, max_results)

        with DDGS() as ddgs:
            results = list(
                ddgs.text(
                    query=query,
                    max_results=max_results,
                    region="fr-fr",
                    safesearch="off",
                    backend="google,mullvad_google,mojeek",
                )
            )

        formatted_results = []
        for result in results:
            formatted_result = {
                "title": result.get("title", ""),
                "url": result.get("href", ""),
                "snippet": result.get("body", ""),
                "date": result.get("date", ""),
            }
            formatted_results.append(formatted_result)

        return formatted_results
    except RatelimitException as e:
        return [{"error": f"Ratelimit: {str(e)}"}]
    except TimeoutException as e:
        return [{"error": f"Timeout: {str(e)}"}]
    except DDGSException as e:
        return [{"error": f"DDGS: {str(e)}"}]


if __name__ == "__main__":
    asyncio.run(mcp.run_stdio_async())

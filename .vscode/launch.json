{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "cwd": "${workspaceFolder}",
            "args": [],
            "justMyCode": false
        },
        {
            "name": "mcp-server",
            "type": "debugpy",
            "request": "launch",
            "program": "mcp_server.py",
            "cwd": "${workspaceFolder}",
            "args": [],
            "justMyCode": false
        },
        {
            "name": "mcp_stdio_client",
            "type": "debugpy",
            "request": "launch",
            "program": "mcp_stdio_client.py",
            "cwd": "${workspaceFolder}",
            "args": [],
            "justMyCode": false
        },
    ],
}
